import React, { useState, useEffect } from 'react';
import { FormControl, InputLabel, MenuItem, Select, Box, Typography, Chip } from '@mui/material';
import SortIcon from '@mui/icons-material/Sort';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import PersonIcon from '@mui/icons-material/Person';

const MovieSorter = ({ movies, onSortChange, section }) => {
  const [sortBy, setSortBy] = useState('default');
  const [sortDirection, setSortDirection] = useState('asc');
  const [availableSites, setAvailableSites] = useState([]);
  const [selectedSite, setSelectedSite] = useState('all');
  const [availableStars, setAvailableStars] = useState([]);
  const [selectedStar, setSelectedStar] = useState('all');
  
  // تحقق مما إذا كان القسم الحالي هو قسم أفلام النجوم
  const isStarsSection = section === 'أفلام النجوم';

  useEffect(() => {
    // Extract unique sites from movies in this section
    if (movies && movies.length > 0) {
      const sites = [...new Set(movies.map(movie => movie.site))];
      setAvailableSites(sites);
      
      // استخراج أسماء النجوم الفريدة إذا كان القسم هو أفلام النجوم
      if (isStarsSection) {
        const stars = [...new Set(movies.map(movie => movie.starName).filter(Boolean))];
        setAvailableStars(stars);
      }
    }
  }, [movies, isStarsSection]);

  const handleSortChange = (event) => {
    const newSortBy = event.target.value;
    setSortBy(newSortBy);
    
    let sortedMovies = [...movies];
    
    if (newSortBy === 'name') {
      sortedMovies.sort((a, b) => {
        return sortDirection === 'asc' 
          ? a.title.localeCompare(b.title) 
          : b.title.localeCompare(a.title);
      });
    } else if (newSortBy === 'date') {
      sortedMovies.sort((a, b) => {
        const dateA = new Date(a.dateAdded || a.createdAt);
        const dateB = new Date(b.dateAdded || b.createdAt);
        return sortDirection === 'asc' 
          ? dateA - dateB 
          : dateB - dateA;
      });
    } else if (newSortBy === 'site') {
      if (selectedSite !== 'all') {
        sortedMovies = sortedMovies.filter(movie => movie.site === selectedSite);
      }
    } else if (newSortBy === 'starName') {
      // ترتيب حسب اسم النجم
      sortedMovies.sort((a, b) => {
        const starA = a.starName || '';
        const starB = b.starName || '';
        return sortDirection === 'asc' 
          ? starA.localeCompare(starB) 
          : starB.localeCompare(starA);
      });
      
      // تصفية حسب النجم المحدد إذا تم اختيار نجم معين
      if (selectedStar !== 'all') {
        sortedMovies = sortedMovies.filter(movie => movie.starName === selectedStar);
      }
    }
    
    onSortChange(sortedMovies);
  };

  const handleDirectionChange = () => {
    const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    setSortDirection(newDirection);
    
    // Re-sort with new direction
    if (sortBy !== 'default') {
      handleSortChange({ target: { value: sortBy } });
    }
  };

  const handleSiteChange = (event) => {
    const site = event.target.value;
    setSelectedSite(site);
    
    let filteredMovies = [...movies];
    
    if (site !== 'all') {
      filteredMovies = filteredMovies.filter(movie => movie.site === site);
    }
    
    onSortChange(filteredMovies);
  };
  
  const handleStarChange = (event) => {
    const star = event.target.value;
    setSelectedStar(star);
    
    let filteredMovies = [...movies];
    
    if (star !== 'all') {
      filteredMovies = filteredMovies.filter(movie => movie.starName === star);
    }
    
    onSortChange(filteredMovies);
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2, flexWrap: 'wrap' }}>
      <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
        ترتيب الأفلام:
      </Typography>
      
      <FormControl size="small" sx={{ minWidth: 150 }}>
        <InputLabel id="sort-select-label">طريقة الترتيب</InputLabel>
        <Select
          labelId="sort-select-label"
          value={sortBy}
          label="طريقة الترتيب"
          onChange={handleSortChange}
          startAdornment={<SortIcon sx={{ mr: 1 }} />}
        >
          <MenuItem value="default">الافتراضي</MenuItem>
          <MenuItem value="name">حسب الاسم</MenuItem>
          <MenuItem value="date">حسب تاريخ الإضافة</MenuItem>
          <MenuItem value="site">حسب الموقع</MenuItem>
          {isStarsSection && <MenuItem value="starName">حسب اسم النجم</MenuItem>}
        </Select>
      </FormControl>
      
      {sortBy !== 'default' && sortBy !== 'site' && (
        <Chip 
          icon={sortDirection === 'asc' ? <ArrowUpwardIcon /> : <ArrowDownwardIcon />}
          label={sortDirection === 'asc' ? "تصاعدي" : "تنازلي"}
          onClick={handleDirectionChange}
          color="primary"
          variant="outlined"
        />
      )}
      
      {sortBy === 'site' && (
        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel id="site-select-label">اختر الموقع</InputLabel>
          <Select
            labelId="site-select-label"
            value={selectedSite}
            label="اختر الموقع"
            onChange={handleSiteChange}
          >
            <MenuItem value="all">جميع المواقع</MenuItem>
            {availableSites.map(site => (
              <MenuItem key={site} value={site}>{site}</MenuItem>
            ))}
          </Select>
        </FormControl>
      )}
      
      {sortBy === 'starName' && (
        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel id="star-select-label">اختر النجم</InputLabel>
          <Select
            labelId="star-select-label"
            value={selectedStar}
            label="اختر النجم"
            onChange={handleStarChange}
            startAdornment={<PersonIcon sx={{ mr: 1 }} />}
          >
            <MenuItem value="all">جميع النجوم</MenuItem>
            {availableStars.map(star => (
              <MenuItem key={star} value={star}>{star}</MenuItem>
            ))}
          </Select>
        </FormControl>
      )}
    </Box>
  );
};

export default MovieSorter;