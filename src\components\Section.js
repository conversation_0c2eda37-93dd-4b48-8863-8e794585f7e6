// Assuming this is the file that displays movie sections
// I'll add the necessary code to integrate the MovieSorter component

import React, { useState } from 'react';
import MovieSorter from './MovieSorter';
// ... other imports

const Section = ({ title, movies, ...props }) => {
  const [sortedMovies, setSortedMovies] = useState(movies);

  const handleSortChange = (newSortedMovies) => {
    setSortedMovies(newSortedMovies);
  };

  // When the movies prop changes, update sortedMovies
  React.useEffect(() => {
    setSortedMovies(movies);
  }, [movies]);

  return (
    <div className="section">
      <h2>{title}</h2>
      
      {/* Add the MovieSorter component */}
      <MovieSorter 
        movies={movies} 
        onSortChange={handleSortChange} 
        section={title}
      />
      
      <div className="movies-container">
        {/* Display the sorted movies instead of the original movies */}
        {sortedMovies.map(movie => (
          // Your existing movie rendering code
          <MovieCard key={movie.id} movie={movie} />
        ))}
      </div>
    </div>
  );
};

export default Section;